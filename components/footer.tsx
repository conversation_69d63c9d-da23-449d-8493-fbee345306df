import Image from "next/image"

export default function Footer() {
  return (
    <footer className="bg-[#8d56db] py-12 px-6 relative overflow-hidden">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          {/* Left side - Logo and Copyright */}
          <div className="space-y-4">
            <div className="flex items-center">
              <Image
                src="/Landinglogo.svg"
                alt="EverKind"
                width={200}
                height={55}
                className="h-6 sm:h-8 md:h-10 lg:h-12 w-auto"
              />
            </div>
            <p className="text-white/60 text-sm">
              © {new Date().getFullYear()} EverKind. All rights reserved.
            </p>
          </div>

          {/* Right side - Decorative SVG elements */}
          <div className="flex justify-center md:justify-end relative">
            {/* Large teal hexagon outline - positioned to wrap around and extend down right side */}
            <div className="absolute -top-20 -right-12 transform translate-x-16">
              <Image
                src="/96.svg"
                alt=""
                width={240}
                height={264}
                className="w-60 h-auto md:w-72 lg:w-80"
              />
            </div>
            {/* Small dark purple hexagon - positioned in upper left area of the design */}
            <div className="absolute -top-16 right-20 transform translate-x-4 z-10">
              <Image
                src="/95.svg"
                alt=""
                width={80}
                height={88}
                className="w-20 h-auto md:w-24 lg:w-28"
              />
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
